import { validateMessage, validateMessageResponse, type Message, type MessageResponse } from '@/lib/schemas';

export default defineBackground(() => {
  console.log('🚀 Background script initialized!', {
    id: browser.runtime.id,
    timestamp: new Date().toISOString()
  });

  // 监听插件图标点击事件
  browser.action.onClicked.addListener(async (tab) => {
    console.log('🎯 Extension icon clicked!', {
      tab: {
        id: tab.id,
        url: tab.url,
        title: tab.title,
        active: tab.active
      },
      timestamp: new Date().toISOString()
    });

    // 确保tab存在且有有效的ID
    if (!tab.id) {
      console.error('❌ No valid tab ID found', tab);
      return;
    }

    // 检查tab URL是否有效
    if (!tab.url || tab.url.startsWith('chrome://') || tab.url.startsWith('chrome-extension://')) {
      console.warn('⚠️ Cannot inject content script into this page:', tab.url);
      return;
    }

    try {
      console.log('📤 Sending message to content script...');

      // 创建类型安全的消息
      const message: Message = {
        action: 'toggleUI',
        timestamp: Date.now()
      };

      // 验证消息格式
      const validatedMessage = validateMessage(message);
      if (!validatedMessage) {
        console.error('❌ Invalid message format');
        return;
      }

      // 向当前活动标签页发送消息
      const response = await browser.tabs.sendMessage(tab.id, validatedMessage);

      // 验证响应格式
      const validatedResponse = validateMessageResponse(response);
      if (validatedResponse) {
        console.log('✅ Message sent successfully, response:', validatedResponse);
      } else {
        console.warn('⚠️ Invalid response format:', response);
      }
    } catch (error) {
      console.error('❌ Failed to send message to content script:', error);

      // 尝试注入content script（如果还没有注入的话）
      try {
        console.log('🔄 Attempting to inject content script...');
        await browser.scripting.executeScript({
          target: { tabId: tab.id },
          files: ['content-scripts/content.js']
        });
        console.log('✅ Content script injected successfully');

        // 重新尝试发送消息
        setTimeout(async () => {
          try {
            const retryResponse = await browser.tabs.sendMessage(tab.id!, {
              action: 'toggleUI',
              timestamp: Date.now()
            });
            console.log('✅ Retry message sent successfully, response:', retryResponse);
          } catch (retryError) {
            console.error('❌ Retry failed:', retryError);
          }
        }, 1000);

      } catch (injectError) {
        console.error('❌ Failed to inject content script:', injectError);
      }
    }
  });

  // 监听扩展安装/启动事件
  browser.runtime.onInstalled.addListener((details) => {
    console.log('🔧 Extension installed/updated:', details);
  });

  // 监听来自content script的消息
  browser.runtime.onMessage.addListener((message, sender, sendResponse) => {
    console.log('📨 Background received message:', { message, sender });

    // 验证消息格式
    const validatedMessage = validateMessage(message);
    if (!validatedMessage) {
      const errorResponse: MessageResponse = {
        success: false,
        timestamp: Date.now(),
        error: 'Invalid message format'
      };
      sendResponse(errorResponse);
      return;
    }

    // 处理不同类型的消息
    switch (validatedMessage.action) {
      case 'contentScriptReady':
        console.log('✅ Content script ready on:', validatedMessage.url);
        break;
      case 'getSettings':
        // 这里可以返回设置信息
        break;
      default:
        console.log('📝 Received message:', validatedMessage.action);
    }

    const response: MessageResponse = {
      success: true,
      timestamp: Date.now()
    };
    sendResponse(response);
  });
});
