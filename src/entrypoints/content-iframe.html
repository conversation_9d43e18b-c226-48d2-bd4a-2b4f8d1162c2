<!doctype html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="Reader Plugin Content Interface" />
    <title>Reader Plugin</title>

    <!-- Prevent iframe from being indexed -->
    <meta name="robots" content="noindex, nofollow" />

    <!-- Ensure proper rendering in iframe -->
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />

    <!-- Prevent zoom on mobile devices -->
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />

    <!-- Basic styles for the iframe container -->
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        html, body {
            width: 100%;
            height: 100%;
            overflow: hidden;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Can<PERSON>ell', '<PERSON><PERSON> Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        #root {
            width: 100%;
            height: 100%;
            position: relative;
            overflow: auto;
        }

        /* Ensure proper scrolling behavior */
        #root > * {
            min-height: 100%;
        }
    </style>
</head>

<body>
    <!-- React application root -->
    <div id="root"></div>

    <!-- Load the React app script -->
    <script type="module" src="iframe-app.js"></script>
</body>

</html>