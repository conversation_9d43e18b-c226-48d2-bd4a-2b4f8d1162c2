import '~/assets/tailwind.css';
import type { ContentScriptContext } from '#imports';
import ReactDOM from 'react-dom/client';
import App from './App.tsx';
import { getStoreInstances } from '@/store';
import { validateMessage, type Message, type MessageResponse } from '@/lib/schemas';
import { extractArticleContent } from '@/lib/extract/ReadAbility.ts';

// 全局UI实例管理
let uiInstance: globalThis.ShadowRootContentScriptUi<ReactDOM.Root> | null = null;

export default defineContentScript({
	matches: ['<all_urls>'],
	cssInjectionMode: 'ui',
	async main(ctx) {
		// 获取store实例（非React环境安全）
		const { appStore } = getStoreInstances();

		// 初始化应用状态
		appStore.getState().setCurrentUrl(window.location.href);
		appStore.getState().setInitialized(true);

		// 发送初始化消息
		await sendContentReadyMessage();

		// 创建消息生成器实例
		const messageGenerator = getMessage();

		try {
			// 使用 for await 循环持续处理消息
			for await (const message of messageGenerator) {
				await handleMessage(message, ctx);
			}
		} catch (error) {
			console.error('❌ Error in message processing loop:', error);
		}
	},
});

// 处理接收到的消息
async function handleMessage(message: Message, ctx: ContentScriptContext) {
	const sendResponse = (message as any)._sendResponse;

	try {
		console.log('🔄 Processing message:', message.action);

		// 处理不同类型的消息
		switch (message.action) {
			case 'toggleUI':
				await handleToggleUI(ctx);
				break;
			default:
				console.warn('⚠️ Unknown message action:', message.action);
		}

		const successResponse: MessageResponse = {
			success: true,
			timestamp: Date.now(),
		};
		sendResponse(successResponse);
	} catch (error: any) {
		console.error('❌ Error processing message:', error);
		const errorResponse: MessageResponse = {
			success: false,
			timestamp: Date.now(),
			error: error.message,
		};
		sendResponse(errorResponse);
	}
}

// 处理UI切换逻辑
async function handleToggleUI(ctx: ContentScriptContext) {
	const { appStore } = getStoreInstances();
	const appState = appStore.getState();

	const result = await extractArticleContent(document);
	console.log('extractArticleContent', result);

	// 切换UI可见性状态
	appState.setUIVisible(!appState.isUIVisible);

	// 获取更新后的状态
	const updatedState = appStore.getState();

	if (updatedState.isUIVisible) {
		// 显示UI - 如果还没有创建则创建
		console.log('👁️ Showing UI...');
		try {
			if (!uiInstance) {
				uiInstance = await createUI(ctx);
			}

			if (uiInstance) {
				uiInstance.mount();
			}
		} catch (error) {
			console.error('❌ Failed to show UI:', error);
			throw error;
		}
	} else {
		// 隐藏UI
		console.log('👁️ Hiding UI...');
		try {
			if (uiInstance) {
				uiInstance.remove();
				console.log('✅ UI hidden successfully');
			}
		} catch (error) {
			console.error('❌ Failed to hide UI:', error);
			throw error;
		}
	}

	console.log('🎯 UI toggle completed', {
		isUIVisible: updatedState.isUIVisible,
	});
}

// 向background script发送初始化消息
async function sendContentReadyMessage() {
	try {
		const initMessage: Message = {
			action: 'contentScriptReady',
			url: window.location.href,
			timestamp: Date.now(),
		};

		const validatedMessage = validateMessage(initMessage);
		if (validatedMessage) {
			const initResponse = await browser.runtime.sendMessage(validatedMessage);
			console.log('📤 Initialization message sent to background:', initResponse);
		}
	} catch (error) {
		console.warn('⚠️ Could not send initialization message:', error);
	}
}

// 异步生成器函数：持续监听来自background script的消息
async function* getMessage(): AsyncGenerator<Message, void, unknown> {
	// 创建一个Promise队列来处理消息
	const messageQueue: Array<{
		message: Message;
		resolve: (response: MessageResponse) => void;
	}> = [];

	let isListening = true;

	// 设置消息监听器
	const messageListener = (message: any, sender: any, sendResponse: (response: MessageResponse) => void) => {
		console.log('📨 Content script received message:', {
			message,
			sender,
			timestamp: new Date().toISOString(),
		});

		// 验证消息来源
		if (!sender.id || sender.id !== browser.runtime.id) {
			console.warn('⚠️ Message from unknown sender:', sender);
			const errorResponse: MessageResponse = {
				success: false,
				timestamp: Date.now(),
				error: 'Unknown sender',
			};
			sendResponse(errorResponse);
			return;
		}

		// 验证消息格式
		const validatedMessage = validateMessage(message);
		if (!validatedMessage) {
			console.warn('⚠️ Invalid message format:', message);
			const errorResponse: MessageResponse = {
				success: false,
				timestamp: Date.now(),
				error: 'Invalid message format',
			};
			sendResponse(errorResponse);
			return;
		}

		// 将验证后的消息添加到队列
		messageQueue.push({
			message: validatedMessage,
			resolve: sendResponse,
		});
	};

	// 注册消息监听器
	browser.runtime.onMessage.addListener(messageListener);

	try {
		// 持续处理消息队列
		while (isListening) {
			if (messageQueue.length > 0) {
				const { message, resolve } = messageQueue.shift()!;

				// 存储响应函数以便后续使用
				(message as any)._sendResponse = resolve;

				yield message;
			} else {
				// 等待一小段时间再检查队列
				await new Promise((resolve) => setTimeout(resolve, 10));
			}
		}
	} finally {
		// 清理：移除消息监听器
		browser.runtime.onMessage.removeListener(messageListener);
		console.log('🧹 Message listener cleaned up');
	}
}

// 创建UI
async function createUI(ctx: ContentScriptContext): Promise<globalThis.IframeContentScriptUi<unknown>> {
	const baseUrl = browser.runtime.getURL('/');
	const iframeUrl = new URL('content-iframe.html', baseUrl).toString();
	const ui = await createIframeUi(ctx, {
		page: browser.runtime.getURL('content-iframe.html'),
		position: 'inline',
		anchor: 'body',
		onMount: (wrapper, iframe) => {
			// 设置iframe样式
			iframe.width = '100vw';
			iframe.height = '100vh';
			iframe.style.setProperty('border', 'none');
			iframe.style.setProperty('margin', '0');
			iframe.style.setProperty('padding', '0');
			iframe.style.setProperty('box-sizing', 'border-box');
			iframe.style.setProperty('overflow', 'hidden');
			iframe.style.setProperty('position', 'fixed');
			iframe.style.setProperty('top', '0');
			iframe.style.setProperty('left', '0');
			iframe.style.setProperty('width', '100vw');
			iframe.style.setProperty('height', '100vh');
			iframe.style.setProperty('z-index', '999999');
			iframe.style.setProperty('background', '#d3d3d3');
		},
	});
	return ui;
}
