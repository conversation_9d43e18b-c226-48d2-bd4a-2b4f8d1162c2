import { useState, useEffect } from 'react';
import { cn } from '@/lib/utils';
import { useStores } from '@/store';
import Article from './Article';
import Toolbar from './Toolbar';

export default function App() {
	const { settings } = useStores();

	return (
		<div
			className={cn(
				'app min-h-screen transition-colors duration-300',
				`theme-${settings.theme}`,
				`font-${settings.fontFamily}`,
			)}
			style={{
				fontSize: `${settings.fontSize}px`,
				lineHeight: settings.lineHeight,
			}}
		>
			{/* Desktop Toolbar */}
			<div className="fixed right-6 top-1/2 transform -translate-y-1/2 z-50">
				<Toolbar />
			</div>

			{/* Article Content */}
			<Article />
		</div>
	);
}
