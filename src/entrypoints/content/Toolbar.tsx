import { <PERSON>, Sparkles, MoreHorizontal, Minus, Plus, Layout } from 'lucide-react';
import { useStores } from '@/store';
import {
	Button,
	Card,
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuTrigger,
	Toggle,
} from '@/components/ui';
import { THEMES, FAMILYS } from '@/lib/schemas';
import { cn } from '@/lib/utils';

export default function Toolbar() {
	const { settings } = useStores();
	const [aiExtraction, setAIExtraction] = useState(false);
	const [isProcessing, setIsProcessing] = useState(false);

	const handleAIToggle = async () => {
		setIsProcessing(true);
		setAIExtraction(!aiExtraction);
		setIsProcessing(false);
	};

	return (
		<Card
			className="
                backdrop-blur-sm 
                bg-white/60 
                dark:bg-gray-900/60 
                border-white/20 
                dark:border-gray-700/20 
                shadow-sm 
                hover:bg-white/80 
                hover:dark:bg-gray-900/80 
                transition-all 
                duration-200
            "
		>
			<div className="flex flex-col items-center gap-3 p-2 w-10">
				{/* Width Presets - Smaller buttons with reduced visual weight */}
				<DropdownMenu>
					<DropdownMenuTrigger asChild>
						<Button
							variant="ghost"
							size="sm"
							className="w-6 h-6 p-0 opacity-70 hover:opacity-100 transition-opacity"
							title="Content Width"
						>
							<Layout className="w-3 h-3" />
						</Button>
					</DropdownMenuTrigger>
					<DropdownMenuContent side="left">
						<DropdownMenuItem onClick={() => settings.updateSettings({ contentWidth: 520 })}>
							Narrow
						</DropdownMenuItem>
						<DropdownMenuItem onClick={() => settings.updateSettings({ contentWidth: 760 })}>
							Comfortable
						</DropdownMenuItem>
						<DropdownMenuItem onClick={() => settings.updateSettings({ contentWidth: 1200 })}>
							Wide
						</DropdownMenuItem>
					</DropdownMenuContent>
				</DropdownMenu>

				<div className="w-4 h-px bg-border/30" />

				{/* Font Family - Reduced button size and opacity */}
				<DropdownMenu>
					<DropdownMenuTrigger asChild>
						<Button
							variant="ghost"
							size="sm"
							className="w-6 h-6 p-0 opacity-70 hover:opacity-100 transition-opacity"
							title="Font family"
						>
							<Type className="w-3 h-3" />
						</Button>
					</DropdownMenuTrigger>
					<DropdownMenuContent side="left">
						{FAMILYS.map((font) => (
							<DropdownMenuItem
								key={font}
								onClick={() =>
									settings.updateSettings({
										fontFamily: font,
									})
								}
								className={font}
							>
								{font}
							</DropdownMenuItem>
						))}
					</DropdownMenuContent>
				</DropdownMenu>

				{/* Font Size - Smaller controls with subtle styling */}
				<div className="flex flex-col items-center gap-1">
					<Button
						variant="ghost"
						size="sm"
						onClick={() => settings.updateSettings({ fontSize: settings.fontSize + 1 })}
						className="w-6 h-5 p-0 opacity-70 hover:opacity-100 transition-opacity"
						title="Increase font size"
					>
						<Plus className="w-2.5 h-2.5" />
					</Button>
					<span className="text-xs w-6 text-center opacity-60">{settings.fontSize}</span>
					<Button
						variant="ghost"
						size="sm"
						onClick={() => settings.updateSettings({ fontSize: settings.fontSize - 1 })}
						className="w-6 h-5 p-0 opacity-70 hover:opacity-100 transition-opacity"
						title="Decrease font size"
					>
						<Minus className="w-2.5 h-2.5" />
					</Button>
				</div>

				<div className="w-4 h-px bg-border/30" />

				{/* Theme Picker - Smaller color swatches with subtle borders */}
				<div className="flex flex-col items-center gap-2">
					{THEMES.map((theme) => (
						<button
							key={theme}
							className={cn(
								'w-4 h-4 rounded-full border transition-all opacity-80 hover:opacity-100',
								theme === 'light'
									? 'bg-white'
									: theme === 'dark'
									? 'bg-black'
									: theme === 'yellow'
									? 'bg-yellow-50'
									: theme === 'green'
									? 'bg-green-50'
									: 'bg-white',
								settings.theme === theme
									? 'border-gray-600 scale-110 opacity-100'
									: 'border-gray-300/50',
							)}
							onClick={() => settings.updateSettings({ theme: theme })}
							aria-label={`${theme} theme`}
						/>
					))}
				</div>

				<div className="w-4 h-px bg-border/30" />

				{/* AI Extraction - Smaller toggle with reduced visual weight */}
				<Toggle
					pressed={aiExtraction}
					onPressedChange={handleAIToggle}
					disabled={isProcessing}
					aria-label="AI extraction"
					size="sm"
					className="w-6 h-6 p-0 opacity-70 hover:opacity-100 transition-opacity data-[state=on]:opacity-100"
					title={
						isProcessing
							? 'Processing...'
							: aiExtraction
							? 'AI extraction enabled'
							: 'AI extraction disabled'
					}
				>
					<Sparkles className={cn('w-3 h-3', isProcessing && 'animate-spin')} />
				</Toggle>
			</div>
		</Card>
	);
}
