import { createContext, useContext, useRef, useEffect } from 'react';

const PortalContainerContext = createContext<HTMLElement | null>(null);

export function PortalProvider({ children }: { children: React.ReactNode }) {
  const containerRef = useRef<HTMLDivElement>(null);

  return (
    <PortalContainerContext.Provider value={containerRef.current}>
      {children}
      <div ref={containerRef} className="portal-container" />
    </PortalContainerContext.Provider>
  );
}

export function usePortalContainer() {
  return useContext(PortalContainerContext);
}