import '~/assets/tailwind.css';
import ReactDOM from 'react-dom/client';
import App from './entrypoints/content/App.tsx';

export default defineUnlistedScript(() => {
	// 初始化React应用
	function initializeIframeApp() {
		// 获取根元素
		const rootElement = document.getElementById('root');
		if (!rootElement) {
			console.error('❌ Root element not found');
			return;
		}

		// 创建React根实例并渲染应用
		const root = ReactDOM.createRoot(rootElement);
		root.render(<App />);

		console.log('✅ Iframe React app initialized successfully');
	}

	// 当DOM加载完成后初始化应用
	if (document.readyState === 'loading') {
		document.addEventListener('DOMContentLoaded', initializeIframeApp);
	} else {
		initializeIframeApp();
	}
});
