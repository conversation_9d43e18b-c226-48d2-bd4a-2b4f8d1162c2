import { defineConfig } from 'wxt';
import tailwindcss from '@tailwindcss/vite';
import path from 'path';

// See https://wxt.dev/api/config.html
export default defineConfig({
	modules: ['@wxt-dev/module-react'],
	srcDir: 'src',
	manifest: {
		action: {},
		web_accessible_resources: [
			{
				resources: ['content-iframe.html'],
				matches: ['<all_urls>'],
			},
		],
	},
	vite: () => ({
		plugins: [...tailwindcss()] as any,
		resolve: {
			alias: {
				'@': path.resolve(__dirname, './src'),
			},
		},
	}),
});
